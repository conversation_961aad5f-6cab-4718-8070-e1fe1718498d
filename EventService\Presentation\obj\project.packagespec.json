﻿"restore":{"projectUniqueName":"C:\\Users\\<USER>\\RiderProjects\\Ventixe\\EventService\\Presentation\\Presentation.csproj","projectName":"Presentation","projectPath":"C:\\Users\\<USER>\\RiderProjects\\Ventixe\\EventService\\Presentation\\Presentation.csproj","outputPath":"C:\\Users\\<USER>\\RiderProjects\\Ventixe\\EventService\\Presentation\\obj\\","projectStyle":"PackageReference","fallbackFolders":["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"],"originalTargetFrameworks":["net9.0"],"sources":{"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\":{},"C:\\Program Files\\dotnet\\library-packs":{},"https://api.nuget.org/v3/index.json":{}},"frameworks":{"net9.0":{"targetAlias":"net9.0","projectReferences":{"C:\\Users\\<USER>\\RiderProjects\\Ventixe\\EventService\\Application\\Application.csproj":{"projectPath":"C:\\Users\\<USER>\\RiderProjects\\Ventixe\\EventService\\Application\\Application.csproj"},"C:\\Users\\<USER>\\RiderProjects\\Ventixe\\EventService\\Persistence\\Persistence.csproj":{"projectPath":"C:\\Users\\<USER>\\RiderProjects\\Ventixe\\EventService\\Persistence\\Persistence.csproj"}}}},"warningProperties":{"warnAsError":["NU1605"]},"restoreAuditProperties":{"enableAudit":"true","auditLevel":"low","auditMode":"direct"},"SdkAnalysisLevel":"9.0.300"}"frameworks":{"net9.0":{"targetAlias":"net9.0","dependencies":{"Microsoft.AspNetCore.OpenApi":{"target":"Package","version":"[9.0.5, )"},"Microsoft.EntityFrameworkCore.SqlServer":{"target":"Package","version":"[9.0.0, )"},"Microsoft.EntityFrameworkCore.Tools":{"target":"Package","version":"[9.0.0, )"},"Swashbuckle.AspNetCore":{"target":"Package","version":"[7.2.0, )"}},"imports":["net461","net462","net47","net471","net472","net48","net481"],"assetTargetFallback":true,"warn":true,"frameworkReferences":{"Microsoft.AspNetCore.App":{"privateAssets":"none"},"Microsoft.NETCore.App":{"privateAssets":"all"}},"runtimeIdentifierGraphPath":"C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}