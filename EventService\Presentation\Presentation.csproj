<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.5"/>
        <PackageReference Include="Swashbuckle.AspNetCore" Version="7.2.0"/>
        <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.0" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.0" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\Application\Application.csproj" />
      <ProjectReference Include="..\Persistence\Persistence.csproj" />
    </ItemGroup>

</Project>
